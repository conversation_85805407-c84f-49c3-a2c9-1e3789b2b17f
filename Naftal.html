<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculateur Naftal</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            border-radius: 10px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
            display: none;
        }
        .section.active {
            display: block;
        }
        .section h3 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        th {
            background-color: #3498db;
            color: white;
            font-weight: bold;
        }
        input[type="number"] {
            width: 80px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
        }
        .calculated {
            background-color: #e8f5e8;
            font-weight: bold;
        }
        .total-row {
            background-color: #fff3cd;
            font-weight: bold;
        }
        .buttons {
            text-align: center;
            margin-top: 20px;
        }
        button {
            padding: 10px 20px;
            margin: 0 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn-calculate {
            background-color: #28a745;
            color: white;
        }
        .btn-export {
            background-color: #007bff;
            color: white;
        }
        .btn-clear {
            background-color: #dc3545;
            color: white;
        }
        .date-input {
            margin-bottom: 20px;
        }
        .date-input input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-left: 10px;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .summary-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .summary-card h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .summary-card .value {
            font-size: 18px;
            font-weight: bold;
            color: #27ae60;
        }
        .two-columns {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .three-columns {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }
        .ecart-negatif {
            color: red;
        }
        .ecart-positif {
            color: green;
        }
        .institution-table {
            width: 100%;
        }
        .tab-container {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            background-color: #f1f1f1;
            border: 1px solid #ddd;
            border-bottom: none;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
        }
        .tab.active {
            background-color: #3498db;
            color: white;
            border-color: #3498db;
        }
        .products-table {
            width: 100%;
        }
        .products-table th {
            white-space: nowrap;
        }
        .category-header {
            background-color: #e9ecef;
            font-weight: bold;
            text-align: left;
            padding: 8px;
            margin-top: 15px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>NAFTAL - Feuille de Brigade</h1>
            <p>Calculateur automatique pour le pompiste CHELLALI SADDAM</p>
        </div>

        <div class="date-input">
            <label for="date">Date:</label>
            <input type="date" id="date" value="">
        </div>

        <!-- Tab Navigation -->
        <div class="tab-container">
            <div class="tab active" onclick="showSection('index-section')">Index</div>
            <div class="tab" onclick="showSection('vente-section')">Ventes</div>
            <div class="tab" onclick="showSection('recette-section')">Recettes</div>
            <div class="tab" onclick="showSection('paiement-section')">Paiements</div>
            <div class="tab" onclick="showSection('institution-section')">Institutions</div>
            <div class="tab" onclick="showSection('produits-section')">Produits</div>
            <div class="tab" onclick="showSection('bilan-section')">Bilan</div>
        </div>

        <!-- Index Section -->
        <div id="index-section" class="section active">
            <div class="two-columns">
                <!-- Section Index Fermé -->
                <div class="section">
                    <h3>Index Fermé</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Pistolet</th>
                                <th>Super</th>
                                <th>Essence</th>
                                <th>Gasoil</th>
                                <th>Gas-oil VL</th>
                                <th>Gas-oil TR</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>A1</td>
                                <td><input type="number" id="indexA1_super" step="0.01" oninput="calculateAll()"></td>
                                <td><input type="number" id="indexA1_essence" step="0.01" oninput="calculateAll()"></td>
                                <td><input type="number" id="indexA1_gasoil" step="0.01" oninput="calculateAll()"></td>
                                <td><input type="number" id="indexA1_gasoilVL" step="0.01" oninput="calculateAll()"></td>
                                <td><input type="number" id="indexA1_gasoilTR" step="0.01" oninput="calculateAll()"></td>
                            </tr>
                            <!-- Rest of the index fermé table rows -->
                        </tbody>
                    </table>
                </div>

                <!-- Section Index Ouvert (Totaux seulement) -->
                <div class="section">
                    <h3>Index Ouvert (Totaux)</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Carburant</th>
                                <th>Total Index Ouvert</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Super</td>
                                <td><input type="number" id="totalOuvert_super" step="0.01" oninput="calculateAll()"></td>
                            </tr>
                            <!-- Rest of the index ouvert table rows -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Vente Section -->
        <div id="vente-section" class="section">
            <h3>Quantité Vendue (Calculé automatiquement)</h3>
            <table>
                <thead>
                    <tr>
                        <th>Pistolet</th>
                        <th>Super</th>
                        <th>Essence</th>
                        <th>Gasoil</th>
                        <th>Gas-oil VL</th>
                        <th>Gas-oil TR</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Vente table content -->
                </tbody>
            </table>
        </div>

        <!-- Recette Section -->
        <div id="recette-section" class="section">
            <h3>Recette (Calculé automatiquement)</h3>
            <table>
                <thead>
                    <tr>
                        <th>Carburant</th>
                        <th>Quantité (L)</th>
                        <th>Prix (DA)</th>
                        <th>Recette (DA)</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Recette table content -->
                </tbody>
            </table>
        </div>

        <!-- Paiement Section -->
        <div id="paiement-section" class="section">
            <h3>Paiements et Dépenses</h3>
            <table>
                <thead>
                    <tr>
                        <th>Type</th>
                        <th>Montant (DA)</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Paiement table content -->
                </tbody>
            </table>
        </div>

        <!-- Institution Section -->
        <div id="institution-section" class="section">
            <h3>Institutions</h3>
            <div class="three-columns">
                <!-- NAFTAL Table -->
                <div>
                    <table class="institution-table">
                        <thead>
                            <tr>
                                <th colspan="3">NAFTAL</th>
                            </tr>
                            <tr>
                                <th>Montant</th>
                                <th>NBR</th>
                                <th>TOTAL</th>
                            </tr>
                        </thead>
                        <tbody id="naftal-body">
                            <!-- NAFTAL table content -->
                        </tbody>
                    </table>
                </div>

                <!-- DGSN Table -->
                <div>
                    <table class="institution-table">
                        <thead>
                            <tr>
                                <th colspan="3">DGSN</th>
                            </tr>
                            <tr>
                                <th>Montant</th>
                                <th>NBR</th>
                                <th>TOTAL</th>
                            </tr>
                        </thead>
                        <tbody id="dgsn-body">
                            <!-- DGSN table content -->
                        </tbody>
                    </table>
                </div>

                <!-- MDN Table -->
                <div>
                    <table class="institution-table">
                        <thead>
                            <tr>
                                <th colspan="3">MDN</th>
                            </tr>
                            <tr>
                                <th>Montant</th>
                                <th>NBR</th>
                                <th>TOTAL</th>
                            </tr>
                        </thead>
                        <tbody id="mdn-body">
                            <!-- MDN table content -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Total Institutions -->
            <table>
                <tr class="total-row">
                    <td><strong>TOTAL INSTITUTIONS</strong></td>
                    <td class="calculated" id="total_institutions">0.00</td>
                </tr>
            </table>
        </div>

<!-- Produits Section -->
<div id="produits-section" class="section">
    <h3>Produits et Lubrifiants</h3>
    
    <table class="products-table">
        <thead>
            <tr>
                <th>DESIGNATION</th>
                <th>STOCK DEBUT</th>
                <th>APR</th>
                <th>TOTAL STOCK</th>
                <th>FIN BRIG</th>
                <th>VENTE OUT</th>
                <th>PRIX UNT</th>
                <th>VENTES VALUUES</th>
            </tr>
        </thead>
        <tbody id="products-body">
            <!-- GAZ BUTANE B13 -->
            <tr>
                <td>GAZ BUTANE B13</td>
                <td><input type="number" class="stock" value="49" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" value="405" oninput="calculateProduits()"></td>
                <td class="calculated total-block">454</td>
                <td><input type="number" class="fir-brief" value="144" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" value="40" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="200.00" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values">11200.00</td>
            </tr>
            
            <!-- NAFT SIRGAZ IL -->
            <tr>
                <td>NAFT SIRGAZ 1L</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="1049.53" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>
            
            <!-- NAFT 50V80 SL -->
            <tr>
                <td>NAFT SIRGAZ 4L</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="3900.00" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>
            
            <!-- NAFT 5V 10V 40 SL -->
            <tr>
                <td>NAFT 20W50 5L</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="3832.40" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>



            <!-- NAFT 5V 5V 40 Plus -->
            <tr>
                <td>NAFT Sy 10W40 5L</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="4300.00" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>
            
            <tr>
                <td>NAFT Sy 5W40 PLUS</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="4700.00" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>
            <!-- NAFT ECO SV30 -->
            <tr>
                <td>NAFT ECO 5W30</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="2519.37" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>
            
            <!-- NAFT VTS 15V 40 -->
            <tr>
                <td>NAFT VTS 15W40</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="1049.53" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>
            
            <!-- NAFT SV 10V 40 IL -->
            <tr>
                <td>NAFT Sy 10W40 1L</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="3900.00" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>

              <!-- NAFT SV 10V 40 IL -->
            <tr>
                <td>NAFT Sy 10W40 4L</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="2115.66" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>
            
            <!-- CHELLA STO 15V 40 -->
            <tr>
                <td>CHIFFA CHELIA 40</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="3257.36" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>
            
            <!-- Add all other products in the same format -->
               <tr>
                <td>CHELIA STD 15W40</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="2781.73" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>
            
            <tr>
                <td>CHELIA TD 15W40</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="12365.00" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>

             <tr>
                <td>TASSILIA EP 75W80</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="16106.40" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>

            <tr>
                <td>TASSILIA EP 75W80</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="1215.87" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>
            
                        <!-- TASSILLA Destinut -->
            <tr>
                <td>TASSILLA Dextron</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="1162.82" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>

            <!-- LAVE GLACE EL -->
            <tr>
                <td>LAVE GLACE 5L</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="700.00" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>

            <!-- LAVE GLACE 2 L -->
            <tr>
                <td>LAVE GLACE 2L</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="320.00" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>

            <!-- SHAMPOONSOL 1 L -->
            <tr>
                <td>SHAMPOING 1L</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="339.99" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>

            <!-- GLACEOLE 2 L -->
            <tr>
                <td>GLACEOLE 24L</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="3854.51" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>

            <!-- GLACEOLE 3 L -->
            <tr>
                <td>GLACEOLE 5L</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="850.00" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>

            <!-- GLACEOLE 4 L -->
            <tr>
                <td>GLACEOLE 2L</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="399.99" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>

              <tr>
                <td>EAU Déminiralisée</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="100.00" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>

            <!-- GLACEOL G12 2 L -->
            <tr>
                <td>GLACEOL G12 2L</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="795.00" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>

             <tr>
                <td>GLACEOL G12 5L</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="1795.00" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>

            <!-- GLACEOLE G13 2 L -->
            <tr>
                <td>GLACEOLE G13 2L</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="950.00" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>

            <!-- GLACEOLE G13 5 L -->
            <tr>
                <td>GLACEOLC G13 5L</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="2050.00" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>

            <!-- DETENUEUR -->
            <tr>
                <td>DETENDEUR</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="950.00" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>

            <!-- CONSIGNE B13 -->
            <tr>
                <td>CONSIGNE B13</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="6000.00" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>

            <!-- CONSIGNE B06 -->
            <tr>
                <td>CONSIGNE B06</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="1600.00" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>

            <!-- PREUS -->
            <tr>
                <td>PREUS</td>
                <td><input type="number" class="stock" oninput="calculateProduits()"></td>
                <td><input type="number" class="apr" oninput="calculateProduits()"></td>
                <td class="calculated total-block"></td>
                <td><input type="number" class="fir-brief" oninput="calculateProduits()"></td>
                <td><input type="number" class="vente-out" oninput="calculateProduits()"></td>
                <td><input type="number" class="prix-unt" value="0" step="0.01" oninput="calculateProduits()"></td>
                <td class="calculated ventes-values"></td>
            </tr>  
        </tbody>
    </table>

    <div class="category-header">TOTAL PRODUITS</div>
    <table>
        <tr class="total-row">
            <td><strong>TOTAL PISTE</strong></td>
            <td class="calculated" id="total_produits">0.00</td>
        </tr>
    </table>
</div>

        <!-- Bilan Section -->
        <div id="bilan-section" class="section">
            <h3>Bilan Final</h3>
            <table>
                <thead>
                    <tr>
                        <th>Description</th>
                        <th>Montant (DA)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Total Recette Carburants</td>
                        <td class="calculated" id="bilan_recette">0.00</td>
                    </tr>
                    <tr>
                        <td>Total Paiements Reçus</td>
                        <td class="calculated" id="bilan_paiements">0.00</td>
                    </tr>
                    <tr>
                        <td>Total Dépenses</td>
                        <td class="calculated" id="bilan_depenses">0.00</td>
                    </tr>
                    <tr>
                        <td>Total Institutions</td>
                        <td class="calculated" id="bilan_institutions">0.00</td>
                    </tr>
                    <tr>
                        <td>Total Produits</td>
                        <td class="calculated" id="bilan_produits">0.00</td>
                    </tr>
                    <tr class="total-row">
                        <td><strong>Écart Final</strong></td>
                        <td class="calculated" id="bilan_ecart_final">0.00</td>
                    </tr>
                </tbody>
            </table>
            
            <!-- Résumé -->
            <div class="summary">
                <div class="summary-card">
                    <h4>Total Quantité Vendue</h4>
                    <div class="value" id="summary_total_qty">0.00 L</div>
                </div>
                <div class="summary-card">
                    <h4>Total Recette</h4>
                    <div class="value" id="summary_total_recette">0.00 DA</div>
                </div>
                <div class="summary-card">
                    <h4>Écart Final</h4>
                    <div class="value" id="summary_ecart_final">0.00 DA</div>
                </div>
            </div>
        </div>

        <!-- Buttons -->
        <div class="buttons">
            <button class="btn-calculate" onclick="calculateAll()">Recalculer</button>
            <button class="btn-export" onclick="exportToExcel()">Exporter vers Excel</button>
            <button class="btn-clear" onclick="clearAll()">Effacer tout</button>
        </div>
    </div>

    <script>
        // Set today's date
        document.getElementById('date').valueAsDate = new Date();

        // Tab navigation
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });
            
            // Deactivate all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionId).classList.add('active');
            
            // Activate selected tab
            event.currentTarget.classList.add('active');
        }

// Function to calculate products
function calculateProduits() {
    let totalProduits = 0;
    const rows = document.querySelectorAll('#products-body tr');
    
    rows.forEach(row => {
        const stock = parseFloat(row.querySelector('.stock').value) || 0;
        const apr = parseFloat(row.querySelector('.apr').value) || 0;
        const totalBlock = stock + apr;
        
        const totalBlockCell = row.querySelector('.total-block');
        totalBlockCell.textContent = totalBlock.toFixed(0);
        
        const firBrief = parseFloat(row.querySelector('.fir-brief').value) || 0;
        const venteOut = parseFloat(row.querySelector('.vente-out').value) || 0;
        const prixUnt = parseFloat(row.querySelector('.prix-unt').value) || 0;
        const ventesValues = venteOut * prixUnt;
        
        const ventesValuesCell = row.querySelector('.ventes-values');
        ventesValuesCell.textContent = ventesValues.toFixed(2);
        
        totalProduits += ventesValues;
    });
    
    document.getElementById('total_produits').textContent = totalProduits.toFixed(2);
    document.getElementById('bilan_produits').textContent = totalProduits.toFixed(2);
    calculateBilanFinal();
}

// Initialize calculations when page loads
window.onload = function() {
    calculateAll();
};

        // Function to calculate final bilan
        function calculateBilanFinal() {
            const recette = parseFloat(document.getElementById('bilan_recette').textContent) || 0;
            const paiements = parseFloat(document.getElementById('bilan_paiements').textContent) || 0;
            const depenses = parseFloat(document.getElementById('bilan_depenses').textContent) || 0;
            const institutions = parseFloat(document.getElementById('total_institutions').textContent) || 0;
            const produits = parseFloat(document.getElementById('total_produits').textContent.replace(',', '')) || 0;
            
            const ecartFinal = recette + produits - paiements - depenses - institutions;
            
            document.getElementById('bilan_ecart_final').textContent = ecartFinal.toFixed(2);
            document.getElementById('summary_ecart_final').textContent = ecartFinal.toFixed(2) + ' DA';
            
            // Apply color based on final balance
            const ecartElement = document.getElementById('bilan_ecart_final');
            const summaryEcart = document.getElementById('summary_ecart_final');
            
            if (ecartFinal < 0) {
                ecartElement.classList.add('ecart-negatif');
                ecartElement.classList.remove('ecart-positif');
                summaryEcart.classList.add('ecart-negatif');
                summaryEcart.classList.remove('ecart-positif');
            } else {
                ecartElement.classList.add('ecart-positif');
                ecartElement.classList.remove('ecart-negatif');
                summaryEcart.classList.add('ecart-positif');
                summaryEcart.classList.remove('ecart-negatif');
            }
        }

        // Modify calculateAll to include products calculation
        function calculateAll() {
            calculateQuantities();
            calculateRecettes();
            calculatePaiementsDepenses();
            calculateBilan();
            calculateInstitutions();
            calculateProduits();
            updateSummary();
        }

        // Existing functions (calculateQuantities, calculateRecettes, etc.) remain the same
        // Just make sure to call calculateBilanFinal() where appropriate

        // Modify exportToExcel to include products data
        function exportToExcel() {
            const date = document.getElementById('date').value;
            const wb = XLSX.utils.book_new();
            
            // Create export data
            const data = [];
            
            // Header
            data.push(['NAFTAL - Feuille de Brigade']);
            data.push(['Date: ' + date]);
            data.push(['Pompiste: CHELLALI SADDAM']);
            data.push([]);
            
            // Existing export data...
            
            // Add Products data
            data.push([]);
            data.push(['PRODUITS ET LUBRIFIANTS']);
            data.push([]);
            
            // GAZ BUTANE
            data.push(['GAZ BUTANE']);
            data.push(['DESIGNATION', 'STOCK DRIM', 'APR', 'TOTAL BLOCK', 'FIR BRIEF', 'VENTE OUT', 'PRIX UNT', 'VENTES VALUUES']);
            data.push([
                'GAZ BUTANE B13',
                parseFloat(document.getElementById('gaz_stock').value) || 0,
                parseFloat(document.getElementById('gaz_apr').value) || 0,
                parseFloat(document.getElementById('gaz_total_block').textContent) || 0,
                parseFloat(document.getElementById('gaz_fir_brief').value) || 0,
                parseFloat(document.getElementById('gaz_vente_out').value) || 0,
                parseFloat(document.getElementById('gaz_prix_unt').value) || 0,
                parseFloat(document.getElementById('gaz_ventes_values').textContent) || 0
            ]);
            data.push([]);
            
            // Other product categories
            productCategories.forEach(category => {
                data.push([category.name]);
                data.push(['DESIGNATION', 'STOCK DRIM', 'APR', 'TOTAL BLOCK', 'FIR BRIEF', 'VENTE OUT', 'PRIX UNT', 'VENTES VALUUES']);
                
                category.products.forEach(product => {
                    data.push([product, '', '', '', '', '', '', '']);
                });
                
                data.push([]);
            });
            
            data.push(['TOTAL PRODUITS', parseFloat(document.getElementById('total_produits').textContent.replace(',', '')) || 0]);
            
            // Create Excel sheet
            const ws = XLSX.utils.aoa_to_sheet(data);
            XLSX.utils.book_append_sheet(wb, ws, 'Feuille de Brigade');
            
            // Save file
            const fileName = `Naftal_Brigade_${date || 'sans_date'}.xlsx`;
            XLSX.writeFile(wb, fileName);
        }

        // Initialize products tables
        function initializeProductsTables() {
            const container = document.getElementById('produits-section');
            
            productCategories.forEach(category => {
                // Create category header
                const header = document.createElement('div');
                header.className = 'category-header';
                header.textContent = category.name;
                container.appendChild(header);
                
                // Create table
                const table = document.createElement('table');
                table.className = 'products-table';
                
                // Create table header
                const thead = document.createElement('thead');
                thead.innerHTML = `
                    <tr>
                        <th>DESIGNATION</th>
                        <th>STOCK DRIM</th>
                        <th>APR</th>
                        <th>TOTAL BLOCK</th>
                        <th>FIR BRIEF</th>
                        <th>VENTE OUT</th>
                        <th>PRIX UNT</th>
                        <th>VENTES VALUUES</th>
                    </tr>
                `;
                table.appendChild(thead);
                
                // Create table body
                const tbody = document.createElement('tbody');
                
                category.products.forEach(product => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${product}</td>
                        <td><input type="number" oninput="calculateProduits()"></td>
                        <td><input type="number" oninput="calculateProduits()"></td>
                        <td class="calculated"></td>
                        <td><input type="number" oninput="calculateProduits()"></td>
                        <td><input type="number" oninput="calculateProduits()"></td>
                        <td><input type="number" step="0.01" oninput="calculateProduits()"></td>
                        <td class="calculated"></td>
                    `;
                    tbody.appendChild(row);
                });
                
                table.appendChild(tbody);
                container.appendChild(table);
            });
        }

        // Call initialization when page loads
        window.onload = function() {
            initializeProductsTables();
            calculateAll();
        };
    </script>
</body>
</html>