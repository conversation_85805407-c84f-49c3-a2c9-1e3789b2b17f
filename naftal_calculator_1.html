

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculateur Naftal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        /* Fixed background image with opacity */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('5072612.jpg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            opacity: 0.6;
            z-index: -1;
        }

        /* Custom styles for calculated cells */
        .calculated {
            background-color: #dcfce7 !important;
        }
        .total-row {
            background-color: #fef3c7 !important;
        }
        .ecart-negatif {
            color: #dc2626 !important;
        }
        .ecart-positif {
            color: #16a34a !important;
        }

        /* Responsive table wrapper */
        .table-wrapper {
            overflow-x: auto;
        }

        /* Custom input styling */
        input[type="number"] {
            width: 80px;
        }

        @media (max-width: 640px) {
            input[type="number"] {
                width: 60px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body class="min-h-screen bg-gray-100 font-sans">
    <div class="container mx-auto max-w-7xl p-4 sm:p-6 lg:p-8">
        <div class="bg-white/90 backdrop-blur-sm rounded-xl shadow-2xl overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-slate-800 to-blue-600 text-white text-center p-6 sm:p-8">
                <h1 class="text-2xl sm:text-3xl lg:text-4xl font-bold mb-2">NAFTAL - Feuille de Brigade</h1>
                <p class="text-sm sm:text-base lg:text-lg opacity-90">Calculateur automatique pour le pompiste CHELLALI SADDAM</p>
            </div>

            <div class="p-4 sm:p-6 lg:p-8">
                <!-- Date Input -->
                <div class="mb-6 sm:mb-8">
                    <label for="date" class="block text-sm font-medium text-gray-700 mb-2">Date:</label>
                    <input type="date" id="date" value="" class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Two Columns Layout -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Section Index Fermé -->
                    <div class="bg-gray-50/80 backdrop-blur-sm rounded-lg border-2 border-gray-200 p-4 sm:p-6">
                        <h3 class="text-lg sm:text-xl font-semibold text-slate-800 border-b-2 border-blue-500 pb-3 mb-4">Index Fermé</h3>
                        <div class="table-wrapper">
                            <table class="w-full border-collapse mb-4">
                                <thead>
                                    <tr>
                                        <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Pistolet</th>
                                        <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Essence 1</th>
                                        <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Essence 2</th>
                                        <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Essence 3</th>
                                        <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Gas-oil VL</th>
                                        <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Gas-oil TR</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">A1</td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexA1_super" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexA1_essence" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexA1_gasoil" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexA1_gasoilVL" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexA1_gasoilTR" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                    </tr>
                                    <tr>
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">B1</td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexB1_super" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexB1_essence" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexB1_gasoil" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexB1_gasoilVL" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexB1_gasoilTR" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                    </tr>
                                    <tr>
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">A2</td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexA2_super" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexA2_essence" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexA2_gasoil" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexA2_gasoilVL" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexA2_gasoilTR" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                    </tr>
                                    <tr>
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">B2</td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexB2_super" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexB2_essence" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexB2_gasoil" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexB2_gasoilVL" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexB2_gasoilTR" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                    </tr>
                                    <tr>
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">A3</td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexA3_super" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexA3_essence" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexA3_gasoil" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexA3_gasoilVL" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexA3_gasoilTR" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                    </tr>
                                    <tr>
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">B3</td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexB3_super" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexB3_essence" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexB3_gasoil" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexB3_gasoilVL" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexB3_gasoilTR" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                    </tr>
                                    <tr>
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">A4</td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexA4_super" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexA4_essence" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexA4_gasoil" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexA4_gasoilVL" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexA4_gasoilTR" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                    </tr>
                                    <tr>
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">B4</td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexB4_super" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexB4_essence" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexB4_gasoil" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexB4_gasoilVL" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                        <td class="border border-gray-300 p-1"><input type="number" id="indexB4_gasoilTR" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                    </tr>
                                    <tr class="total-row bg-yellow-100">
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold">TT_INDEX_FERM</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold" id="totalFerm_super">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold" id="totalFerm_essence">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold" id="totalFerm_gasoil">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold" id="totalFerm_gasoilVL">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold" id="totalFerm_gasoilTR">0.00</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Section Index Ouvert (Totaux seulement) -->
                    <div class="bg-gray-50/80 backdrop-blur-sm rounded-lg border-2 border-gray-200 p-4 sm:p-6">
                        <h3 class="text-lg sm:text-xl font-semibold text-slate-800 border-b-2 border-blue-500 pb-3 mb-4">Index Ouvert (Totaux)</h3>
                        <div class="table-wrapper">
                            <table class="w-full border-collapse mb-4">
                                <thead>
                                    <tr>
                                        <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Carburant</th>
                                        <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Total Index Ouvert</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Essence 1</td>
                                        <td class="border border-gray-300 p-1 text-center"><input type="number" id="totalOuvert_super" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                    </tr>
                                    <tr>
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Essence 2</td>
                                        <td class="border border-gray-300 p-1 text-center"><input type="number" id="totalOuvert_essence" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                    </tr>
                                    <tr>
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Essence 3</td>
                                        <td class="border border-gray-300 p-1 text-center"><input type="number" id="totalOuvert_gasoil" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                    </tr>
                                    <tr>
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Gas-oil VL</td>
                                        <td class="border border-gray-300 p-1 text-center"><input type="number" id="totalOuvert_gasoilVL" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                    </tr>
                                    <tr>
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Gas-oil TR</td>
                                        <td class="border border-gray-300 p-1 text-center"><input type="number" id="totalOuvert_gasoilTR" step="0.01" oninput="calculateAll()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Section Quantité Vendue (Calculé automatiquement) -->
                <div class="bg-gray-50/80 backdrop-blur-sm rounded-lg border-2 border-gray-200 p-4 sm:p-6 mb-8">
                    <h3 class="text-lg sm:text-xl font-semibold text-slate-800 border-b-2 border-blue-500 pb-3 mb-4">Quantité Vendue (Calculé automatiquement)</h3>
                    <div class="table-wrapper">
                            <table class="w-full border-collapse mb-4">
                                <thead>
                                    <tr>
                                        <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Pistolet</th>
                                        <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Super</th>
                                        <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Essence</th>
                                        <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Gasoil</th>
                                        <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Gas-oil VL</th>
                                        <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Gas-oil TR</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">A1</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyA1_super">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyA1_essence">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyA1_gasoil">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyA1_gasoilVL">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyA1_gasoilTR">0.00</td>
                                    </tr>
                                    <tr>
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">B1</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyB1_super">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyB1_essence">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyB1_gasoil">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyB1_gasoilVL">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyB1_gasoilTR">0.00</td>
                                    </tr>
                                    <tr>
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">A2</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyA2_super">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyA2_essence">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyA2_gasoil">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyA2_gasoilVL">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyA2_gasoilTR">0.00</td>
                                    </tr>
                                    <tr>
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">B2</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyB2_super">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyB2_essence">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyB2_gasoil">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyB2_gasoilVL">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyB2_gasoilTR">0.00</td>
                                    </tr>
                                    <tr>
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">A3</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyA3_super">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyA3_essence">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyA3_gasoil">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyA3_gasoilVL">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyA3_gasoilTR">0.00</td>
                                    </tr>
                                    <tr>
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">B3</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyB3_super">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyB3_essence">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyB3_gasoil">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyB3_gasoilVL">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyB3_gasoilTR">0.00</td>
                                    </tr>
                                    <tr>
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">A4</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyA4_super">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyA4_essence">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyA4_gasoil">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyA4_gasoilVL">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyA4_gasoilTR">0.00</td>
                                    </tr>
                                    <tr>
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">B4</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyB4_super">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyB4_essence">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyB4_gasoil">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyB4_gasoilVL">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="qtyB4_gasoilTR">0.00</td>
                                    </tr>
                                    <tr class="total-row bg-yellow-100">
                                        <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold">TOTAL</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold bg-yellow-200" id="totalQty_super">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold bg-yellow-200" id="totalQty_essence">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold bg-yellow-200" id="totalQty_gasoil">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold bg-yellow-200" id="totalQty_gasoilVL">0.00</td>
                                        <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold bg-yellow-200" id="totalQty_gasoilTR">0.00</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                <!-- Section Prix -->
                <div class="bg-gray-50/80 backdrop-blur-sm rounded-lg border-2 border-gray-200 p-4 sm:p-6 mb-8">
                    <h3 class="text-lg sm:text-xl font-semibold text-slate-800 border-b-2 border-blue-500 pb-3 mb-4">Prix du Litre</h3>
                    <div class="table-wrapper">
                        <table class="w-full border-collapse mb-4">
                            <thead>
                                <tr>
                                    <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Carburant</th>
                                    <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Prix (DA)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Super</td>
                                    <td class="border border-gray-300 p-1 text-center"><input type="number" id="prix_super" step="0.01" value="45.62" oninput="calculateAll()" class="w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center"></td>
                                </tr>
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Essence 1</td>
                                    <td class="border border-gray-300 p-1 text-center"><input type="number" id="prix_essence" step="0.01" value="45.62" oninput="calculateAll()" class="w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center"></td>
                                </tr>
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Essence 2</td>
                                    <td class="border border-gray-300 p-1 text-center"><input type="number" id="prix_gasoil" step="0.01" value="45.62" oninput="calculateAll()" class="w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center"></td>
                                </tr>
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Essence 3</td>
                                    <td class="border border-gray-300 p-1 text-center"><input type="number" id="prix_gasoilVL" step="0.01" value="29.01" oninput="calculateAll()" class="w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center"></td>
                                </tr>
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Gas-oil TR</td>
                                    <td class="border border-gray-300 p-1 text-center"><input type="number" id="prix_gasoilTR" step="0.01" value="29.01" oninput="calculateAll()" class="w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Section Recette -->
                <div class="bg-gray-50/80 backdrop-blur-sm rounded-lg border-2 border-gray-200 p-4 sm:p-6 mb-8">
                    <h3 class="text-lg sm:text-xl font-semibold text-slate-800 border-b-2 border-blue-500 pb-3 mb-4">Recette (Calculé automatiquement)</h3>
                    <div class="table-wrapper">
                        <table class="w-full border-collapse mb-4">
                            <thead>
                                <tr>
                                    <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Carburant</th>
                                    <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Quantité (L)</th>
                                    <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Prix (DA)</th>
                                    <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Recette (DA)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Super</td>
                                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="recette_qty_super">0.00</td>
                                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="recette_prix_super">0.00</td>
                                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="recette_total_super">0.00</td>
                                </tr>
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Essence</td>
                                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="recette_qty_essence">0.00</td>
                                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="recette_prix_essence">0.00</td>
                                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="recette_total_essence">0.00</td>
                                </tr>
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Gasoil</td>
                                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="recette_qty_gasoil">0.00</td>
                                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="recette_prix_gasoil">0.00</td>
                                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="recette_total_gasoil">0.00</td>
                                </tr>
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Gas-oil VL</td>
                                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="recette_qty_gasoilVL">0.00</td>
                                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="recette_prix_gasoilVL">0.00</td>
                                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="recette_total_gasoilVL">0.00</td>
                                </tr>
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Gas-oil TR</td>
                                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="recette_qty_gasoilTR">0.00</td>
                                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="recette_prix_gasoilTR">0.00</td>
                                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="recette_total_gasoilTR">0.00</td>
                                </tr>
                                <tr class="total-row bg-yellow-100">
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold">TOTAL RECETTE</td>
                                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold bg-yellow-200" id="total_qty_all">0.00</td>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold">-</td>
                                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold bg-yellow-200" id="total_recette_all">0.00</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
       

       

                <!-- Section TPE -->
                <div class="bg-gray-50/80 backdrop-blur-sm rounded-lg border-2 border-gray-200 p-4 sm:p-6 mb-8">
                    <h3 class="text-lg sm:text-xl font-semibold text-slate-800 border-b-2 border-blue-500 pb-3 mb-4">Institutions</h3>
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
                        <!-- NAFTAL Table -->
                        <div class="bg-white/80 backdrop-blur-sm rounded-lg border border-gray-300 p-3 sm:p-4">
                            <table class="w-full border-collapse text-xs sm:text-sm">
                                <thead>
                                    <tr>
                                        <th colspan="3" class="bg-green-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">NAFTAL</th>
                                    </tr>
                                    <tr>
                                        <th class="bg-green-500 text-white font-semibold p-1 text-xs border border-gray-300">Montant</th>
                                        <th class="bg-green-500 text-white font-semibold p-1 text-xs border border-gray-300">NBR</th>
                                        <th class="bg-green-500 text-white font-semibold p-1 text-xs border border-gray-300">TOTAL</th>
                                    </tr>
                                </thead>
                                <tbody id="naftal-body">
                                    <tr>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-green-50" id="naftal_1" data-prix="1200.00">1200.00</td>
                                        <td class="border border-gray-300 p-1 text-center"><input type="number" id="naftal_nbr_1" step="1" value="0" oninput="calculateInstitutions()" class="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 text-center"></td>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-green-50" id="naftal_total_1">0.00</td>
                                    </tr>
                                    <tr>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-green-50" id="naftal_2" data-prix="850.00">850.00</td>
                                        <td class="border border-gray-300 p-1 text-center"><input type="number" id="naftal_nbr_2" step="1" value="0" oninput="calculateInstitutions()" class="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 text-center"></td>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-green-50" id="naftal_total_2">0.00</td>
                                    </tr>
                                    <tr>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-green-50" id="naftal_3" data-prix="270.00">270.00</td>
                                        <td class="border border-gray-300 p-1 text-center"><input type="number" id="naftal_nbr_3" step="1" value="0" oninput="calculateInstitutions()" class="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 text-center"></td>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-green-50" id="naftal_total_3">0.00</td>
                                    </tr>
                                    <tr>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-green-50" id="naftal_4" data-prix="180.00">180.00</td>
                                        <td class="border border-gray-300 p-1 text-center"><input type="number" id="naftal_nbr_4" step="1" value="0" oninput="calculateInstitutions()" class="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 text-center"></td>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-green-50" id="naftal_total_4">0.00</td>
                                    </tr>
                                    <tr class="total-row bg-green-100">
                                        <td colspan="2" class="border border-gray-300 p-1 text-center text-xs font-bold">TOTAL NAFTAL</td>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-bold bg-green-200" id="total_naftal">0.00</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- DGSN Table -->
                        <div class="bg-white/80 backdrop-blur-sm rounded-lg border border-gray-300 p-3 sm:p-4">
                            <table class="w-full border-collapse text-xs sm:text-sm">
                                <thead>
                                    <tr>
                                        <th colspan="3" class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">DGSN</th>
                                    </tr>
                                    <tr>
                                        <th class="bg-blue-500 text-white font-semibold p-1 text-xs border border-gray-300">Montant</th>
                                        <th class="bg-blue-500 text-white font-semibold p-1 text-xs border border-gray-300">NBR</th>
                                        <th class="bg-blue-500 text-white font-semibold p-1 text-xs border border-gray-300">TOTAL</th>
                                    </tr>
                                </thead>
                                <tbody id="dgsn-body">
                                    <tr>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-green-50" id="dgsn_1" data-prix="9149.40">9149.40</td>
                                        <td class="border border-gray-300 p-1 text-center"><input type="number" id="dgsn_nbr_1" step="1" value="0" oninput="calculateInstitutions()" class="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center"></td>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-blue-50" id="dgsn_total_1">0.00</td>
                                    </tr>
                                    <tr>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-green-50" id="dgsn_2" data-prix="912.40">912.40</td>
                                        <td class="border border-gray-300 p-1 text-center"><input type="number" id="dgsn_nbr_2" step="1" value="0" oninput="calculateInstitutions()" class="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center"></td>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-blue-50" id="dgsn_total_2">0.00</td>
                                    </tr>
                                    <tr>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-green-50" id="dgsn_3" data-prix="580.20">580.20</td>
                                        <td class="border border-gray-300 p-1 text-center"><input type="number" id="dgsn_nbr_3" step="1" value="0" oninput="calculateInstitutions()" class="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center"></td>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-blue-50" id="dgsn_total_3">0.00</td>
                                    </tr>
                                    <tr>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-green-50" id="dgsn_4" data-prix="459.70">459.70</td>
                                        <td class="border border-gray-300 p-1 text-center"><input type="number" id="dgsn_nbr_4" step="1" value="0" oninput="calculateInstitutions()" class="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center"></td>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-blue-50" id="dgsn_total_4">0.00</td>
                                    </tr>
                                    <tr>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-green-50" id="dgsn_5" data-prix="456.20">456.20</td>
                                        <td class="border border-gray-300 p-1 text-center"><input type="number" id="dgsn_nbr_5" step="1" value="0" oninput="calculateInstitutions()" class="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center"></td>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-blue-50" id="dgsn_total_5">0.00</td>
                                    </tr>
                                    <tr>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-green-50" id="dgsn_6" data-prix="290.10">290.10</td>
                                        <td class="border border-gray-300 p-1 text-center"><input type="number" id="dgsn_nbr_6" step="1" value="0" oninput="calculateInstitutions()" class="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center"></td>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-blue-50" id="dgsn_total_6">0.00</td>
                                    </tr>
                                    <tr class="total-row bg-blue-100">
                                        <td colspan="2" class="border border-gray-300 p-1 text-center text-xs font-bold">TOTAL DGSN</td>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-bold bg-blue-200" id="total_dgsn">0.00</td>
                                    </tr>
                                </tbody>
                    </table>
                </div>

                        <!-- MDN Table -->
                        <div class="bg-white/80 backdrop-blur-sm rounded-lg border border-gray-300 p-3 sm:p-4">
                            <table class="w-full border-collapse text-xs sm:text-sm">
                                <thead>
                                    <tr>
                                        <th colspan="3" class="bg-purple-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">MDN</th>
                                    </tr>
                                    <tr>
                                        <th class="bg-purple-500 text-white font-semibold p-1 text-xs border border-gray-300">Montant</th>
                                        <th class="bg-purple-500 text-white font-semibold p-1 text-xs border border-gray-300">NBR</th>
                                        <th class="bg-purple-500 text-white font-semibold p-1 text-xs border border-gray-300">TOTAL</th>
                                    </tr>
                                </thead>
                                <tbody id="mdn-body">
                                    <tr>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-green-50" id="mdn_1" data-prix="912.40">912.40</td>
                                        <td class="border border-gray-300 p-1 text-center"><input type="number" id="mdn_nbr_1" step="1" value="0" oninput="calculateInstitutions()" class="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-purple-500 text-center"></td>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-purple-50" id="mdn_total_1">0.00</td>
                                    </tr>
                                    <tr>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-green-50" id="mdn_2" data-prix="1450.50">1450.50</td>
                                        <td class="border border-gray-300 p-1 text-center"><input type="number" id="mdn_nbr_2" step="1" value="0" oninput="calculateInstitutions()" class="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-purple-500 text-center"></td>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-semibold bg-purple-50" id="mdn_total_2">0.00</td>
                                    </tr>
                                    <tr class="total-row bg-purple-100">
                                        <td colspan="2" class="border border-gray-300 p-1 text-center text-xs font-bold">TOTAL MDN</td>
                                        <td class="calculated border border-gray-300 p-1 text-center text-xs font-bold bg-purple-200" id="total_mdn">0.00</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Total Institutions -->
                    <div class="mt-6">
                        <table class="w-full border-collapse bg-white/90 backdrop-blur-sm rounded-lg overflow-hidden shadow-md">
                            <tr class="total-row bg-gradient-to-r from-slate-600 to-slate-700">
                                <td class="border border-gray-300 p-3 text-center text-sm sm:text-base font-bold text-white">TOTAL INSTITUTIONS</td>
                                <td class="calculated border border-gray-300 p-3 text-center text-sm sm:text-base font-bold bg-yellow-200 text-slate-800" id="total_institutions">0.00</td>
                            </tr>
                        </table>
                    </div>
                </div>
       
<!-- Add this after the Institutions section -->
<!-- GPLC Section -->
<div class="bg-gray-50/80 backdrop-blur-sm rounded-lg border-2 border-gray-200 p-4 sm:p-6 mb-8">
    <h3 class="text-lg sm:text-xl font-semibold text-slate-800 border-b-2 border-blue-500 pb-3 mb-4">GPLC</h3>
    <div class="table-wrapper">
        <table class="w-full border-collapse mb-4">
            <thead>
                <tr>
                    <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Description</th>
                    <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Valeur</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Index-fermé A1</td>
                    <td class="border border-gray-300 p-1 text-center"><input type="number" id="gplc_index_ferme_a1" step="0.01" oninput="calculateGPLC()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                </tr>
                <tr>
                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Index-fermé B1</td>
                    <td class="border border-gray-300 p-1 text-center"><input type="number" id="gplc_index_ferme_b1" step="0.01" oninput="calculateGPLC()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                </tr>
                <tr>
                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Index-fermé A2</td>
                    <td class="border border-gray-300 p-1 text-center"><input type="number" id="gplc_index_ferme_a2" step="0.01" oninput="calculateGPLC()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                </tr>
                <tr>
                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Index-fermé B2</td>
                    <td class="border border-gray-300 p-1 text-center"><input type="number" id="gplc_index_ferme_b2" step="0.01" oninput="calculateGPLC()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                </tr>
                <tr class="bg-yellow-100">
                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold">INDEX FER</td>
                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold bg-yellow-200" id="gplc_index_fer">0.00</td>
                </tr>
                <tr>
                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">INDEX OUVER</td>
                    <td class="border border-gray-300 p-1 text-center"><input type="number" id="gplc_index_ouver" step="0.01" oninput="calculateGPLC()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                </tr>
                <tr class="bg-green-100">
                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold">QUANT VENDU</td>
                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold bg-green-200" id="gplc_quant_vendu">0.00</td>
                </tr>
                <tr>
                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">PRIX DU LITRE</td>
                    <td class="border border-gray-300 p-1 text-center"><input type="number" id="gplc_prix_litre" step="0.01" value="9.00" oninput="calculateAll()" class="w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center"></td>
                </tr>
                <tr class="bg-blue-100">
                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold">RECETTE</td>
                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold bg-blue-200" id="gplc_recette">0.00</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

                <!-- Produits Section -->
                <div id="produits-section" class="bg-gray-50/80 backdrop-blur-sm rounded-lg border-2 border-gray-200 p-4 sm:p-6 mb-8">
                    <h3 class="text-lg sm:text-xl font-semibold text-slate-800 border-b-2 border-blue-500 pb-3 mb-4">Produits et Lubrifiants</h3>

                    <div class="table-wrapper">
                        <table class="w-full border-collapse mb-4">
                            <thead>
                                <tr>
                                    <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">DESIGNATION</th>
                                    <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">STOCK DEBUT</th>
                                    <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">APR</th>
                                    <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">TOTAL STOCK</th>
                                    <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">FIN BRIG</th>
                                    <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">VENTE OUT</th>
                                    <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">PRIX UNT</th>
                                    <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">VENTES VALUES</th>
                                </tr>
                            </thead>
                            <tbody id="products-body">
                                <!-- GAZ BUTANE B13 -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">GAZ BUTANE B13</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="200.00">200.00</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50">11200.00</td>
                                </tr>

                                <!-- NAFT SIRGAZ IL -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">NAFT SIRGAZ 1L</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="1049.53">1049.53</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>
            
                                <!-- NAFT 50V80 SL -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">NAFT SIRGAZ 4L</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="3900.00">3900.00</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>
            
                                <!-- NAFT 5V 10V 40 SL -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">NAFT 20W50 5L</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="2266.00">2266.00</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>



                                <!-- NAFT 5V 5V 40 Plus -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">NAFT Sy 10W40 5L</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="3832.40">3832.40</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">NAFT Sy 5W40 PLUS</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="4300.00">4300.00</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>
                                <!-- NAFT ECO SV30 -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">NAFT ECO 5W30</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="4700.00">4700.00</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                <!-- NAFT VTS 15V 40 -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">NAFT VPS 15W40</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="2519.37">2519.37</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>
            
                                <!-- NAFT SV 10V 40 IL -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">NAFT Sy 10W40 1L</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="1049.53">1049.53</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                <!-- NAFT SV 10V 40 IL -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">NAFT Sy 10W40 4L</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="3900.00">3900.00</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>
            
                                <!-- CHELLA STO 15V 40 -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">CHIFFA CHELIA 40</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="2115.66">2115.66</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                <!-- Add all other products in the same format -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">CHELIA STD 15W40</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="3257.36">3257.36</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>
            
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">CHELIA TD 15W40</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="2781.73">2781.73</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                 <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">CH TD 15W40</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="12365.00">12365.00</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">TASSILIA EP 75W80</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="16106.40">16106.40</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">TASSILIA EP 75W80</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="1215.87">1215.87</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                <!-- TASSILLA Destinut -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">TASSILLA Dextron</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="1162.82">1162.82</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                <!-- LAVE GLACE EL -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">LAVE GLACE 5L</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="700.00">700.00</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                <!-- LAVE GLACE 2 L -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">LAVE GLACE 2L</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="320.00">320.00</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                <!-- SHAMPOONSOL 1 L -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">SHAMPOING 1L</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="339.99">339.99</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                <!-- GLACEOLE 2 L -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">GLACEOLE 24L</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="3854.51">3854.51</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                <!-- GLACEOLE 3 L -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">GLACEOLE 5L</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="850.00">850.00</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                <!-- GLACEOLE 4 L -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">GLACEOLE 2L</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="399.99">399.99</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">EAU Déminiralisée</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="100.00">100.00</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                <!-- GLACEOL G12 2 L -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">GLACEOL G12 2L</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="795.00">795.00</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">GLACEOL G12 5L</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="1795.00">1795.00</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                <!-- GLACEOLE G13 2 L -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">GLACEOLE G13 2L</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="950.00">950.00</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                <!-- GLACEOLE G13 5 L -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">GLACEOLC G13 5L</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="2050.00">2050.00</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                <!-- DETENUEUR -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">DETENDEUR</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="950.00">950.00</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                <!-- CONSIGNE B13 -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">CONSIGNE B13</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="6000.00">6000.00</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                <!-- CONSIGNE B06 -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">CONSIGNE B06</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="1600.00">1600.00</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>

                                <!-- PREUS -->
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">PREUS</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="0">0</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>
                                
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">L'HUILE</td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="stock w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="apr w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated total-block border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                    <td class="border border-gray-300 p-1"><input type="number" class="fir-brief w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center" oninput="calculateProduits()"></td>
                                    <td class="calculated vente-out border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-yellow-50">0.00</td>
                                    <td class="calculated prix-unt border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" data-prix="1">0</td>
                                    <td class="calculated ventes-values border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50"></td>
                                </tr>
                            </tbody>
                        </table>

                        <!-- Total Products Section -->
                        <div class="mt-6">
                            <table class="w-full border-collapse bg-white/90 backdrop-blur-sm rounded-lg overflow-hidden shadow-md">
                                <tr class="total-row bg-gradient-to-r from-green-600 to-green-700">
                                    <td class="border border-gray-300 p-3 text-center text-sm sm:text-base font-bold text-white">TOTAL PRODUITS</td>
                                    <td class="calculated border border-gray-300 p-3 text-center text-sm sm:text-base font-bold bg-yellow-200 text-slate-800" id="total_produits">0.00</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

<!-- BILAN FINANCIER Section -->
<div class="bg-gray-50/80 backdrop-blur-sm rounded-lg border-2 border-gray-200 p-4 sm:p-6 mb-8">
    <h3 class="text-lg sm:text-xl font-semibold text-slate-800 border-b-2 border-blue-500 pb-3 mb-4">BILAN FINANCIER</h3>
    <div class="table-wrapper">
        <table class="w-full border-collapse mb-4">
            <tbody>
                <!-- Revenus -->
                <tr class="bg-green-100">
                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold">+ TOTAL CBR (Recettes)</td>
                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="total_cbr">0.00</td>
                </tr>
                <tr class="bg-green-100">
                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold">+ TOTAL PISTE (Produits)</td>
                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-green-50" id="total_piste">0.00</td>
                </tr>
                <tr class="bg-blue-100">
                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold">= PISTE+CBR</td>
                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-blue-50" id="piste_cbr">0.00</td>
                </tr>

                <!-- Déductions -->
                <tr class="bg-red-100">
                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold">- TOTAL TAC (Institutions)</td>
                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-semibold bg-red-50" id="total_tac_display">0.00</td>
                </tr>
                <tr>
                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">- TOTAL TPE</td>
                    <td class="border border-gray-300 p-1 text-center"><input type="number" id="total_tpe" step="0.01" oninput="calculateBilanFinancier()" class="w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center"></td>
                </tr>
                <tr>
                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">- TOTAL CIB - P 300</td>
                    <td class="border border-gray-300 p-1 text-center"><input type="number" id="cib_p300" step="0.01" oninput="calculateBilanFinancier()" class="w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center"></td>
                </tr>
                <tr>
                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">- TOTAL CIB - P 746</td>
                    <td class="border border-gray-300 p-1 text-center"><input type="number" id="cib_p746" step="0.01" oninput="calculateBilanFinancier()" class="w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center"></td>
                </tr>

                <!-- Résultat final -->
                <tr class="bg-yellow-100">
                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold">= TT ESPECE</td>
                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold bg-yellow-200" id="ti_especie">0.00</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>



                <!-- Nouvelle Section: Paiements et Dépenses -->
                <div class="bg-gray-50/80 backdrop-blur-sm rounded-lg border-2 border-gray-200 p-4 sm:p-6 mb-8">
                    <h3 class="text-lg sm:text-xl font-semibold text-slate-800 border-b-2 border-blue-500 pb-3 mb-4">Paiements et Dépenses</h3>
                    <div class="table-wrapper">
                        <table class="w-full border-collapse mb-4">
                            <thead>
                                <tr>
                                    <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Type</th>
                                    <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Montant (DA)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Av1</td>
                                    <td class="border border-gray-300 p-1 text-center"><input type="number" id="paiement_av1" step="0.01" oninput="calculatePaiementsDepenses()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                </tr>
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Av2</td>
                                    <td class="border border-gray-300 p-1 text-center"><input type="number" id="paiement_av2" step="0.01" oninput="calculatePaiementsDepenses()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                </tr>
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Av3</td>
                                    <td class="border border-gray-300 p-1 text-center"><input type="number" id="paiement_av3" step="0.01" oninput="calculatePaiementsDepenses()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                </tr>
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Av4</td>
                                    <td class="border border-gray-300 p-1 text-center"><input type="number" id="paiement_av4" step="0.01" oninput="calculatePaiementsDepenses()" class="w-full px-3 py-2 text-sm sm:text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center min-w-[120px]"></td>
                                </tr>
                                <tr>
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-medium">Espèce</td>
                                    <td class="border border-gray-300 p-1 text-center"><input type="number" id="paiement_espece" step="0.01" oninput="calculatePaiementsDepenses()" class="w-full px-2 py-1 text-xs sm:text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-center"></td>
                                </tr>
                                <tr class="total-row bg-yellow-100">
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold">TOTAL PAIEMENTS</td>
                                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold bg-yellow-200" id="total_paiements">0.00</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
        

                <!-- Nouvelle Section: Bilan Final -->
                <div class="bg-gray-50/80 backdrop-blur-sm rounded-lg border-2 border-gray-200 p-4 sm:p-6 mb-8">
                    <h3 class="text-lg sm:text-xl font-semibold text-slate-800 border-b-2 border-blue-500 pb-3 mb-4">Bilan Final</h3>
                    <div class="table-wrapper">
                        <table class="w-full border-collapse mb-4">
                            <thead>
                                <tr>
                                    <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Description</th>
                                    <th class="bg-blue-600 text-white font-bold p-2 text-xs sm:text-sm border border-gray-300">Montant (DA)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="bg-blue-100">
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold">TI ESPECIE</td>
                                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold bg-blue-200" id="bilan_ti_especie">0.00</td>
                                </tr>
                                <tr class="bg-green-100">
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold">Total Paiements Reçus</td>
                                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold bg-green-200" id="bilan_paiements">0.00</td>
                                </tr>
                                <tr class="total-row bg-yellow-100">
                                    <td class="border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold">Écart Final</td>
                                    <td class="calculated border border-gray-300 p-2 text-center text-xs sm:text-sm font-bold bg-yellow-200" id="bilan_ecart">0.00</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
        </div>

                <!-- Hidden elements for calculations -->
                <div style="display: none;">
                    <span id="bilan_produits">0.00</span>
                    <span id="bilan_depenses">0.00</span>
                </div>

                <!-- Résumé -->
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mt-8">
                    <div class="bg-gray-50/90 backdrop-blur-sm p-4 sm:p-6 rounded-lg border-l-4 border-blue-500 shadow-md">
                        <h4 class="text-sm sm:text-base font-semibold text-slate-800 mb-2">Total Quantité Vendue</h4>
                        <div class="text-lg sm:text-xl font-bold text-green-600" id="summary_total_qty">0.00 L</div>
                    </div>
                    <div class="bg-gray-50/90 backdrop-blur-sm p-4 sm:p-6 rounded-lg border-l-4 border-blue-500 shadow-md">
                        <h4 class="text-sm sm:text-base font-semibold text-slate-800 mb-2">Total Recette</h4>
                        <div class="text-lg sm:text-xl font-bold text-green-600" id="summary_total_recette">0.00 DA</div>
                    </div>
                    <div class="bg-gray-50/90 backdrop-blur-sm p-4 sm:p-6 rounded-lg border-l-4 border-blue-500 shadow-md">
                        <h4 class="text-sm sm:text-base font-semibold text-slate-800 mb-2">Écart</h4>
                        <div class="text-lg sm:text-xl font-bold text-green-600" id="summary_ecart">0.00 DA</div>
                    </div>
                </div>

                <!-- Boutons -->
                <div class="flex flex-col sm:flex-row justify-center items-center gap-4 mt-8 mb-6">
                    <button onclick="calculateAll()" class="w-full sm:w-auto px-6 py-3 bg-green-600 hover:bg-green-700 text-white font-semibold rounded-lg shadow-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                        Recalculer
                    </button>
                    <button onclick="exportToExcel()" class="w-full sm:w-auto px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg shadow-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        Exporter vers Excel
                    </button>
                    <button onclick="clearAll()" class="w-full sm:w-auto px-6 py-3 bg-red-600 hover:bg-red-700 text-white font-semibold rounded-lg shadow-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                        Effacer tout
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Définir la date d'aujourd'hui
        document.getElementById('date').valueAsDate = new Date();

        // Liste des pistolets
        const pistolets = ['A1', 'B1', 'A2', 'B2', 'A3', 'B3', 'A4', 'B4'];
        const carburants = ['super', 'essence', 'gasoil', 'gasoilVL', 'gasoilTR'];

 // Function to calculate institutions
        function calculateInstitutions() {
            // Calculate NAFTAL
            let totalNaftal = 0;
            for (let i = 1; i <= 4; i++) {
                const amountElement = document.getElementById(`naftal_${i}`);
                const amount = amountElement ? (parseFloat(amountElement.dataset.prix) || parseFloat(amountElement.value) || 0) : 0;
                const nbr = parseFloat(document.getElementById(`naftal_nbr_${i}`).value) || 0;
                const total = amount * nbr;
                document.getElementById(`naftal_total_${i}`).textContent = total.toFixed(2);
                totalNaftal += total;
            }
            document.getElementById('total_naftal').textContent = totalNaftal.toFixed(2);

            // Calculate DGSN
            let totalDgsn = 0;
            for (let i = 1; i <= 6; i++) {
                const amountElement = document.getElementById(`dgsn_${i}`);
                const amount = amountElement ? (parseFloat(amountElement.dataset.prix) || parseFloat(amountElement.value) || 0) : 0;
                const nbr = parseFloat(document.getElementById(`dgsn_nbr_${i}`).value) || 0;
                const total = amount * nbr;
                document.getElementById(`dgsn_total_${i}`).textContent = total.toFixed(2);
                totalDgsn += total;
            }
            document.getElementById('total_dgsn').textContent = totalDgsn.toFixed(2);

            // Calculate MDN
            let totalMdn = 0;
            for (let i = 1; i <= 2; i++) {
                const amountElement = document.getElementById(`mdn_${i}`);
                const amount = amountElement ? (parseFloat(amountElement.dataset.prix) || parseFloat(amountElement.value) || 0) : 0;
                const nbr = parseFloat(document.getElementById(`mdn_nbr_${i}`).value) || 0;
                const total = amount * nbr;
                document.getElementById(`mdn_total_${i}`).textContent = total.toFixed(2);
                totalMdn += total;
            }
            document.getElementById('total_mdn').textContent = totalMdn.toFixed(2);

            // Calculate total institutions
            const totalInstitutions = totalNaftal + totalDgsn + totalMdn;
            document.getElementById('total_institutions').textContent = totalInstitutions.toFixed(2);
        }

        // Function to calculate products
function calculateProduits() {
    let totalProduits = 0;
    const rows = document.querySelectorAll('#products-body tr');

    rows.forEach(row => {
        const stock = parseFloat(row.querySelector('.stock').value) || 0;
        const apr = parseFloat(row.querySelector('.apr').value) || 0;
        const totalBlock = stock + apr;

        const totalBlockCell = row.querySelector('.total-block');
        totalBlockCell.textContent = totalBlock.toFixed(0);

        const finBrig = parseFloat(row.querySelector('.fir-brief').value) || 0;

        // Calculate VENTE OUT = TOTAL STOCK - FIN BRIG
        const venteOut = totalBlock - finBrig;
        const venteOutCell = row.querySelector('.vente-out');
        venteOutCell.textContent = venteOut.toFixed(2);

        const prixUntElement = row.querySelector('.prix-unt');
        const prixUnt = prixUntElement ? (parseFloat(prixUntElement.dataset.prix) || parseFloat(prixUntElement.value) || 0) : 0;
        const ventesValues = venteOut * prixUnt;

        const ventesValuesCell = row.querySelector('.ventes-values');
        ventesValuesCell.textContent = ventesValues.toFixed(2);

        totalProduits += ventesValues;
    });

    document.getElementById('total_produits').textContent = totalProduits.toFixed(2);
    document.getElementById('bilan_produits').textContent = totalProduits.toFixed(2);

    // Ensure all calculations are updated
    calculateBilanFinancier();
    calculateBilan();
    updateSummary();
}

// Function to calculate GPLC section
function calculateGPLC() {
    // Get the 4 index-fermé values
    const indexFermeA1 = parseFloat(document.getElementById('gplc_index_ferme_a1').value) || 0;
    const indexFermeB1 = parseFloat(document.getElementById('gplc_index_ferme_b1').value) || 0;
    const indexFermeA2 = parseFloat(document.getElementById('gplc_index_ferme_a2').value) || 0;
    const indexFermeB2 = parseFloat(document.getElementById('gplc_index_ferme_b2').value) || 0;

    // Calculate INDEX FER (sum of all 4 index-fermé)
    const indexFer = indexFermeA1 + indexFermeB1 + indexFermeA2 + indexFermeB2;
    document.getElementById('gplc_index_fer').textContent = indexFer.toFixed(2);

    // Get INDEX OUVER
    const indexOuver = parseFloat(document.getElementById('gplc_index_ouver').value) || 0;

    // Calculate QUANT VENDU = INDEX FER - INDEX OUVER
    const quantVendu = indexFer - indexOuver;
    document.getElementById('gplc_quant_vendu').textContent = quantVendu.toFixed(2);

    // Get PRIX DU LITRE
    const prixLitre = parseFloat(document.getElementById('gplc_prix_litre').value) || 0;

    // Calculate RECETTE = QUANT VENDU × PRIX DU LITRE
    const recette = quantVendu * prixLitre;
    document.getElementById('gplc_recette').textContent = recette.toFixed(2);

    // Update overall calculations
    calculateBilanFinal();
}

// Function to calculate final bilan including products
function calculateBilanFinal() {
    calculateBilanFinancier();
    calculateBilan();
    updateSummary();
}

// Initialize calculations when page loads
window.onload = function() {
    calculateAll();
};



function calculateBilanFinancier() {
    // Get TOTAL CBR (from fuel recettes + GPLC recettes)
    const totalRecetteFuel = parseFloat(document.getElementById('total_recette_all').textContent) || 0;
    const gplcRecette = parseFloat(document.getElementById('gplc_recette').textContent) || 0;
    const totalCbr = totalRecetteFuel + gplcRecette;
    document.getElementById('total_cbr').textContent = totalCbr.toFixed(2);

    // Get TOTAL PISTE (from products)
    const totalPiste = parseFloat(document.getElementById('bilan_produits').textContent) || 0;
    document.getElementById('total_piste').textContent = totalPiste.toFixed(2);

    // Calculate PISTE+CBR
    const pisteCbr = totalCbr + totalPiste;
    document.getElementById('piste_cbr').textContent = pisteCbr.toFixed(2);

    // Get TOTAL TAC (from institutions)
    const totalTac = parseFloat(document.getElementById('total_institutions').textContent) || 0;
    document.getElementById('total_tac_display').textContent = totalTac.toFixed(2);

    // Get user inputs
    const totalTpe = parseFloat(document.getElementById('total_tpe').value) || 0;
    const cibP300 = parseFloat(document.getElementById('cib_p300').value) || 0;
    const cibP746 = parseFloat(document.getElementById('cib_p746').value) || 0;

    // Calculate TI ESPECIE = TOTAL CBR + TOTAL PISTE - (TOTAL TAC + TOTAL TPE + TOTAL CIB - P 300 + TOTAL CIB - P 746)
    const tiEspecie = totalCbr + totalPiste - (totalTac + totalTpe + cibP300 + cibP746);
    document.getElementById('ti_especie').textContent = tiEspecie.toFixed(2);

    return tiEspecie;
}


        // Modify the calculateAll function to include institutions calculation
function calculateAll() {
    calculateQuantities();
    calculateRecettes();
    calculatePaiementsDepenses();
    calculateInstitutions();
    calculateProduits();
    calculateGPLC();
    calculateBilanFinancier();
    calculateBilan();
    updateSummary();
}
       


        function calculateQuantities() {
    // Initialiser les totaux des index fermés
    let totalsFermes = {
        super: 0,
        essence: 0,
        gasoil: 0,
        gasoilVL: 0,
        gasoilTR: 0
    };

    // Calculer les totaux des index fermés
    pistolets.forEach(pistolet => {
        carburants.forEach(carburant => {
            const indexFerme = parseFloat(document.getElementById(`index${pistolet}_${carburant}`).value) || 0;
            totalsFermes[carburant] += indexFerme;
        });
    });

    // Afficher les totaux des index fermés
    carburants.forEach(carburant => {
        document.getElementById(`totalFerm_${carburant}`).textContent = totalsFermes[carburant].toFixed(2);
    });

    // Calculer les quantités vendues pour chaque pistolet
    carburants.forEach(carburant => {
        const totalIndexOuvert = parseFloat(document.getElementById(`totalOuvert_${carburant}`).value) || 0;
        const totalIndexFerme = totalsFermes[carburant];

        // Calculer la quantité totale vendue pour ce carburant
        const quantiteTotale =  totalIndexFerme - totalIndexOuvert;

        // Répartir proportionnellement entre les pistolets selon leurs index fermés
        let totalQtyCalculated = 0;
        pistolets.forEach((pistolet, index) => {
            const indexFerme = parseFloat(document.getElementById(`index${pistolet}_${carburant}`).value) || 0;

            let quantitePistolet = 0;
            if (totalIndexFerme > 0) {
                // Répartition proportionnelle basée sur l'index fermé de chaque pistolet
                quantitePistolet = (indexFerme / totalIndexFerme) * quantiteTotale;
            }

            // Pour le dernier pistolet, ajuster pour éviter les erreurs d'arrondi
            if (index === pistolets.length - 1) {
                quantitePistolet = quantiteTotale - totalQtyCalculated;
            } else {
                totalQtyCalculated += quantitePistolet;
            }

            document.getElementById(`qty${pistolet}_${carburant}`).textContent = quantitePistolet.toFixed(2);
        });

        // Afficher le total des quantités vendues
        document.getElementById(`totalQty_${carburant}`).textContent = quantiteTotale.toFixed(2);
    });
}



        function calculateRecettes() {
            carburants.forEach(carburant => {
                const totalQty = parseFloat(document.getElementById(`totalQty_${carburant}`).textContent) || 0;
                const prix = parseFloat(document.getElementById(`prix_${carburant}`).value) || 0;
                const recette = totalQty * prix;

                document.getElementById(`recette_qty_${carburant}`).textContent = totalQty.toFixed(2);
                document.getElementById(`recette_prix_${carburant}`).textContent = prix.toFixed(2);
                document.getElementById(`recette_total_${carburant}`).textContent = recette.toFixed(2);
            });

            // Calculer les totaux généraux
            let totalQtyAll = 0;
            let totalRecetteAll = 0;

            carburants.forEach(carburant => {
                const qty = parseFloat(document.getElementById(`recette_qty_${carburant}`).textContent) || 0;
                const recette = parseFloat(document.getElementById(`recette_total_${carburant}`).textContent) || 0;
                totalQtyAll += qty;
                totalRecetteAll += recette;
            });

            document.getElementById('total_qty_all').textContent = totalQtyAll.toFixed(2);
            document.getElementById('total_recette_all').textContent = totalRecetteAll.toFixed(2);
        }

        function calculatePaiementsDepenses() {
            // Get values from the new structure (Av1, Av2, Av3, Av4, Espèce)
            const av1 = parseFloat(document.getElementById('paiement_av1').value) || 0;
            const av2 = parseFloat(document.getElementById('paiement_av2').value) || 0;
            const av3 = parseFloat(document.getElementById('paiement_av3').value) || 0;
            const av4 = parseFloat(document.getElementById('paiement_av4').value) || 0;
            const espece = parseFloat(document.getElementById('paiement_espece').value) || 0;

            // Calculate total: sum of all 5 fields
            const totalPaiements = av1 + av2 + av3 + av4 + espece;

            // Update display fields
            document.getElementById('total_paiements').textContent = totalPaiements.toFixed(2);
            document.getElementById('bilan_paiements').textContent = totalPaiements.toFixed(2);

            // For now, set depenses to 0 since we don't have separate depenses anymore
            document.getElementById('bilan_depenses').textContent = '0.00';

            // Trigger overall calculations
            calculateBilanFinancier();
            calculateBilan();
            updateSummary();
        }

       function calculateBilan() {
    // Get values for the new simplified Bilan Final
    const tiEspecie = parseFloat(document.getElementById('ti_especie').textContent) || 0;
    const totalPaiements = parseFloat(document.getElementById('total_paiements').textContent) || 0;

    // Update Bilan Final fields
    document.getElementById('bilan_ti_especie').textContent = tiEspecie.toFixed(2);
    document.getElementById('bilan_paiements').textContent = totalPaiements.toFixed(2);

    // Calculate Écart Final = TI ESPECIE - Total Paiements Reçus
    const ecartFinal = totalPaiements - tiEspecie;

    const ecartElement = document.getElementById('bilan_ecart');
    if (ecartElement) {
        ecartElement.textContent = ecartFinal.toFixed(2);

        // Apply color based on Écart Final
        ecartElement.classList.remove('ecart-negatif', 'ecart-positif');
        if (ecartFinal < 0) {
            ecartElement.classList.add('ecart-negatif');
        } else {
            ecartElement.classList.add('ecart-positif');
        }
    }
}

        function updateSummary() {
            // Get values from the correct elements with error checking
            const totalQty = parseFloat(document.getElementById('total_qty_all')?.textContent || '0') || 0;
            const totalRecetteCarburants = parseFloat(document.getElementById('total_recette_all')?.textContent || '0') || 0;
            const gplcRecette = parseFloat(document.getElementById('gplc_recette')?.textContent || '0') || 0;
            const totalProduits = parseFloat(document.getElementById('bilan_produits')?.textContent || '0') || 0;

            // Get the new Écart Final from Bilan Final
            const ecartFinal = parseFloat(document.getElementById('bilan_ecart')?.textContent || '0') || 0;

            // Total revenue includes carburants, GPLC, and products
            const totalRecette = totalRecetteCarburants + gplcRecette + totalProduits;

            // Update summary cards with error checking
            const summaryQtyElement = document.getElementById('summary_total_qty');
            const summaryRecetteElement = document.getElementById('summary_total_recette');
            const ecartSummary = document.getElementById('summary_ecart');

            if (summaryQtyElement) summaryQtyElement.textContent = totalQty.toFixed(2) + ' L';
            if (summaryRecetteElement) summaryRecetteElement.textContent = totalRecette.toFixed(2) + ' DA';

            // Use Écart Final (TI ESPECIE - Total Paiements) in summary
            if (ecartSummary) {
                ecartSummary.textContent = ecartFinal.toFixed(2) + ' DA';

                // Apply color coding: RED if negative, GREEN if positive
                ecartSummary.classList.remove('ecart-negatif', 'ecart-positif', 'text-green-600', 'text-red-600');
                if (ecartFinal < 0) {
                    ecartSummary.classList.add('ecart-negatif', 'text-red-600');
                } else {
                    ecartSummary.classList.add('ecart-positif', 'text-green-600');
                }
            }
        }

        function exportToExcel() {
            const date = document.getElementById('date').value;
            const wb = XLSX.utils.book_new();
            
            // Créer les données pour l'export
            const data = [];
            
            // En-tête
            data.push(['NAFTAL - Feuille de Brigade']);
            data.push(['Date: ' + date]);
            data.push(['Pompiste: CHELLALI SADDAM']);
            data.push([]);
            
            // Index Fermé
            data.push(['INDEX FERMÉ']);
            data.push(['Pistolet', 'Super', 'Essence', 'Gasoil', 'Gas-oil VL', 'Gas-oil TR']);
            
            pistolets.forEach(pistolet => {
                const row = [pistolet];
                carburants.forEach(carburant => {
                    const value = document.getElementById(`index${pistolet}_${carburant}`).value || 0;
                    row.push(parseFloat(value));
                });
                data.push(row);
            });
            
            data.push([]);
            
            // Index Ouvert (Totaux)
            data.push(['INDEX OUVERT (TOTAUX)']);
            data.push(['Carburant', 'Total Index Ouvert']);
            carburants.forEach(carburant => {
                const value = document.getElementById(`totalOuvert_${carburant}`).value || 0;
                data.push([getCarburantName(carburant), parseFloat(value)]);
            });
            
            data.push([]);
            
            // Quantité Vendue
            data.push(['QUANTITÉ VENDUE']);
            data.push(['Pistolet', 'Super', 'Essence', 'Gasoil', 'Gas-oil VL', 'Gas-oil TR']);
            
            pistolets.forEach(pistolet => {
                const row = [pistolet];
                carburants.forEach(carburant => {
                    const value = document.getElementById(`qty${pistolet}_${carburant}`).textContent;
                    row.push(parseFloat(value));
                });
                data.push(row);
            });
            
            // Total Quantité
            const totalRow = ['TOTAL'];
            carburants.forEach(carburant => {
                const value = document.getElementById(`totalQty_${carburant}`).textContent;
                totalRow.push(parseFloat(value));
            });
            data.push(totalRow);
            
            data.push([]);
            
            // Prix
            data.push(['PRIX DU LITRE']);
            data.push(['Carburant', 'Prix (DA)']);
            carburants.forEach(carburant => {
                const prix = document.getElementById(`prix_${carburant}`).value || 0;
                data.push([getCarburantName(carburant), parseFloat(prix)]);
            });
            
            data.push([]);
            
            // Recette
            data.push(['RECETTE']);
            data.push(['Carburant', 'Quantité (L)', 'Prix (DA)', 'Recette (DA)']);
            carburants.forEach(carburant => {
                const carburantName = getCarburantName(carburant);
                const qty = document.getElementById(`recette_qty_${carburant}`).textContent;
                const prix = document.getElementById(`recette_prix_${carburant}`).textContent;
                const recette = document.getElementById(`recette_total_${carburant}`).textContent;
                data.push([carburantName, parseFloat(qty), parseFloat(prix), parseFloat(recette)]);
            });
            
            // Total Recette
            const totalQtyAll = document.getElementById('total_qty_all').textContent;
            const totalRecetteAll = document.getElementById('total_recette_all').textContent;
            data.push(['TOTAL', parseFloat(totalQtyAll), '-', parseFloat(totalRecetteAll)]);
            
            data.push([]);

              // Add Institutions data to export
            data.push([]);
            data.push(['INSTITUTIONS']);
            data.push([]);
            
            // NAFTAL
            data.push(['NAFTAL']);
            data.push(['Montant', 'NBR', 'TOTAL']);
            for (let i = 1; i <= 4; i++) {
                const amount = parseFloat(document.getElementById(`naftal_${i}`).value) || 0;
                const nbr = parseFloat(document.getElementById(`naftal_nbr_${i}`).value) || 0;
                const total = parseFloat(document.getElementById(`naftal_total_${i}`).textContent) || 0;
                data.push([amount, nbr, total]);
            }
            data.push(['TOTAL NAFTAL', '', parseFloat(document.getElementById('total_naftal').textContent)]);
            data.push([]);
            
            // DGSN
            data.push(['DGSN']);
            data.push(['Montant', 'NBR', 'TOTAL']);
            for (let i = 1; i <= 6; i++) {
                const amount = parseFloat(document.getElementById(`dgsn_${i}`).value) || 0;
                const nbr = parseFloat(document.getElementById(`dgsn_nbr_${i}`).value) || 0;
                const total = parseFloat(document.getElementById(`dgsn_total_${i}`).textContent) || 0;
                data.push([amount, nbr, total]);
            }
            data.push(['TOTAL DGSN', '', parseFloat(document.getElementById('total_dgsn').textContent)]);
            data.push([]);
            
            // MDN
            data.push(['MDN']);
            data.push(['Montant', 'NBR', 'TOTAL']);
            for (let i = 1; i <= 2; i++) {
                const amount = parseFloat(document.getElementById(`mdn_${i}`).value) || 0;
                const nbr = parseFloat(document.getElementById(`mdn_nbr_${i}`).value) || 0;
                const total = parseFloat(document.getElementById(`mdn_total_${i}`).textContent) || 0;
                data.push([amount, nbr, total]);
            }
            data.push(['TOTAL MDN', '', parseFloat(document.getElementById('total_mdn').textContent)]);
            data.push([]);
            
            // Total Institutions
            data.push(['TOTAL INSTITUTIONS', parseFloat(document.getElementById('total_institutions').textContent)]);
            
             // Add GPLC data
    data.push([]);
    data.push(['GPLC']);
    data.push(['Description', 'Super', 'Essence']);
    ['a1', 'b1', 'a2', 'b2'].forEach(pistolet => {
        const superVal = parseFloat(document.getElementById(`gplc_${pistolet}_super`).value) || 0;
        const essenceVal = parseFloat(document.getElementById(`gplc_${pistolet}_essence`).value) || 0;
        data.push([`Index-fermé ${pistolet.toUpperCase()}`, superVal, essenceVal]);
    });
    
    // Add TOTAL CBR data
    data.push([]);
    data.push(['TOTAL CBR']);
    data.push(['+ TOTAL PISTE', parseFloat(document.getElementById('total_piste').textContent)]);
    data.push(['= PISTE+CBR', parseFloat(document.getElementById('piste_cbr').textContent)]);
    data.push(['-TOTAL TAC', parseFloat(document.getElementById('total_tac').value) || 0]);
    data.push(['-TOTAL TPE', parseFloat(document.getElementById('total_tpe').value) || 0]);
    
    // Add TOTAL CIB data
    data.push([]);
    data.push(['TOTAL CIB']);
    data.push(['+ TOTAL CIB - P 300', parseFloat(document.getElementById('cib_p300').value) || 0]);
    data.push(['+ TOTAL CIB - P 746', parseFloat(document.getElementById('cib_p746').value) || 0]);
    data.push(['= TT ESPECE', parseFloat(document.getElementById('ti_especie').textContent)]);
    

            // Paiements et Dépenses
            data.push(['PAIEMENTS ET DÉPENSES']);
            data.push(['Type', 'Montant (DA)']);
            data.push(['Av1', parseFloat(document.getElementById('paiement_av1').value) || 0]);
            data.push(['Av2', parseFloat(document.getElementById('paiement_av2').value) || 0]);
            data.push(['Av3', parseFloat(document.getElementById('paiement_av3').value) || 0]);
            data.push(['Av4', parseFloat(document.getElementById('paiement_av4').value) || 0]);
            data.push(['Espèce', parseFloat(document.getElementById('paiement_espece').value) || 0]);
            data.push(['TOTAL PAIEMENTS', parseFloat(document.getElementById('total_paiements').textContent)]);
            
            data.push([]);

            // Produits et Lubrifiants
            data.push(['PRODUITS ET LUBRIFIANTS']);
            data.push(['Désignation', 'Stock Début', 'APR', 'Total Stock', 'Fin Brig', 'Vente Out', 'Prix Unit', 'Ventes Values']);

            const productRows = document.querySelectorAll('#products-body tr');
            productRows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length > 0) {
                    const productData = [];
                    productData.push(cells[0].textContent); // Désignation
                    productData.push(parseFloat(cells[1].querySelector('input')?.value) || 0); // Stock Début
                    productData.push(parseFloat(cells[2].querySelector('input')?.value) || 0); // APR
                    productData.push(parseFloat(cells[3].textContent) || 0); // Total Stock
                    productData.push(parseFloat(cells[4].querySelector('input')?.value) || 0); // Fin Brig
                    productData.push(parseFloat(cells[5].querySelector('input')?.value) || 0); // Vente Out
                    productData.push(parseFloat(cells[6].querySelector('input')?.value) || 0); // Prix Unit
                    productData.push(parseFloat(cells[7].textContent) || 0); // Ventes Values
                    data.push(productData);
                }
            });

            data.push(['TOTAL PRODUITS', '', '', '', '', '', '', parseFloat(document.getElementById('total_produits').textContent)]);

            data.push([]);

            // Bilan Final
            data.push(['BILAN FINAL']);
            data.push(['Description', 'Montant (DA)']);
            data.push(['TI ESPECIE', parseFloat(document.getElementById('bilan_ti_especie').textContent)]);
            data.push(['Total Paiements Reçus', parseFloat(document.getElementById('bilan_paiements').textContent)]);
            data.push(['Écart Final', parseFloat(document.getElementById('bilan_ecart').textContent)]);
            
            // Créer la feuille Excel
            const ws = XLSX.utils.aoa_to_sheet(data);
            XLSX.utils.book_append_sheet(wb, ws, 'Feuille de Brigade');
            
            // Sauvegarder le fichier
            const fileName = `Naftal_Brigade_${date || 'sans_date'}.xlsx`;
            XLSX.writeFile(wb, fileName);
        }

        function getCarburantName(carburant) {
            const names = {
                'super': 'Super',
                'essence': 'Essence',
                'gasoil': 'Gasoil',
                'gasoilVL': 'Gas-oil VL',
                'gasoilTR': 'Gas-oil TR'
            };
            return names[carburant] || carburant;
        }

        function clearAll() {
            if (confirm('Êtes-vous sûr de vouloir effacer toutes les données ?')) {
                // Vider tous les champs d'entrée
                const inputs = document.querySelectorAll('input[type="number"]');
                inputs.forEach(input => {
                    if (input.id !== 'prix_super' && input.id !== 'prix_essence' &&
                        input.id !== 'prix_gasoil' && input.id !== 'prix_gasoilVL' &&
                        input.id !== 'prix_gasoilTR' && input.id !== 'depense_cafe') {
                        input.value = '';
                    }
                });

                // Clear GPLC fields (new structure)
                const gplcFields = [
                    'gplc_index_ferme_a1', 'gplc_index_ferme_b1',
                    'gplc_index_ferme_a2', 'gplc_index_ferme_b2',
                    'gplc_index_ouver', 'gplc_prix_litre'
                ];
                gplcFields.forEach(fieldId => {
                    const field = document.getElementById(fieldId);
                    if (field) field.value = '';
                });

                // Clear BILAN FINANCIER fields
                const bilanFields = ['total_tpe', 'cib_p300', 'cib_p746'];
                bilanFields.forEach(fieldId => {
                    const field = document.getElementById(fieldId);
                    if (field) field.value = '';
                });

                // Remettre les valeurs calculées à zéro
                const calculated = document.querySelectorAll('.calculated');
                calculated.forEach(cell => {
                    cell.textContent = '0.00';
                    cell.classList.remove('ecart-negatif', 'ecart-positif');
                });

                // Réinitialiser le résumé
                const summaryQty = document.getElementById('summary_total_qty');
                const summaryRecette = document.getElementById('summary_total_recette');
                const summaryEcart = document.getElementById('summary_ecart');

                if (summaryQty) summaryQty.textContent = '0.00 L';
                if (summaryRecette) summaryRecette.textContent = '0.00 DA';
                if (summaryEcart) {
                    summaryEcart.textContent = '0.00 DA';
                    summaryEcart.classList.remove('ecart-negatif', 'ecart-positif');
                }

                // Recalculate everything to update all calculated fields
                calculateAll();
            }
        }

        // Calcul initial
        calculateAll();
    </script>
</body>

</html>

